{"class_name": "Sequential", "keras_version": "2.0.6", "config": [{"class_name": "<PERSON><PERSON>", "config": {"kernel_initializer": {"class_name": "RandomUniform", "config": {"maxval": 0.05, "seed": null, "minval": -0.05}}, "name": "dense_1", "kernel_constraint": null, "bias_regularizer": null, "bias_constraint": null, "dtype": "float32", "activation": "relu", "trainable": true, "kernel_regularizer": null, "bias_initializer": {"class_name": "Zeros", "config": {}}, "units": 32, "batch_input_shape": [null, 8], "use_bias": true, "activity_regularizer": null}}, {"class_name": "<PERSON><PERSON>", "config": {"kernel_initializer": {"class_name": "RandomUniform", "config": {"maxval": 0.05, "seed": null, "minval": -0.05}}, "name": "dense_2", "kernel_constraint": null, "bias_regularizer": null, "bias_constraint": null, "activation": "relu", "trainable": true, "kernel_regularizer": null, "bias_initializer": {"class_name": "Zeros", "config": {}}, "units": 16, "use_bias": true, "activity_regularizer": null}}, {"class_name": "<PERSON><PERSON>", "config": {"kernel_initializer": {"class_name": "RandomUniform", "config": {"maxval": 0.05, "seed": null, "minval": -0.05}}, "name": "dense_3", "kernel_constraint": null, "bias_regularizer": null, "bias_constraint": null, "activation": "sigmoid", "trainable": true, "kernel_regularizer": null, "bias_initializer": {"class_name": "Zeros", "config": {}}, "units": 1, "use_bias": true, "activity_regularizer": null}}], "backend": "tensorflow"}