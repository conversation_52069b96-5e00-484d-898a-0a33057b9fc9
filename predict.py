from keras.models import model_from_json
import pandas as pd
from sklearn.model_selection import train_test_split
import joblib

# Load model
json_file = open("model.json", "r")
loaded_model_json = json_file.read()
json_file.close()
loaded_model = model_from_json(loaded_model_json)

# Load weights
loaded_model.load_weights("model.h5")
print("Loaded model from disk")

# Load dataset
df = pd.read_csv("pima-indians-diabetes.data", header=None)
df.columns = ['Pregnancies', 'Glucose', 'BloodPressure', 'SkinThickness', 'Insulin',
              'BMI', 'DiabetesPedigreeFunction', 'Age', 'Outcome']

# Replace invalid 0s
cols_with_zero_as_missing = ['Glucose', 'BloodPressure', 'SkinThickness', 'Insulin', 'BMI']
df[cols_with_zero_as_missing] = df[cols_with_zero_as_missing].replace(0, pd.NA)

# Impute with median
df.fillna(df.median(), inplace=True)

# Split features and target
X = df.drop("Outcome", axis=1)
y = df["Outcome"]

# Load scaler and apply
scaler = joblib.load("scaler.save")
X_scaled = scaler.transform(X)

# Split into validation set
_, X_validation, _, y_validation = train_test_split(X_scaled, y, test_size=0.2, random_state=7)

# Compile model
loaded_model.compile(optimizer="adam", loss="binary_crossentropy", metrics=["accuracy"])

# Evaluate
scores = loaded_model.evaluate(X_validation, y_validation, verbose=0)
print("Validation Accuracy: %.2f%%" % (scores[1] * 100))
